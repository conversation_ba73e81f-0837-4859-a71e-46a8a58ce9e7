import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  On<PERSON><PERSON><PERSON>,
  OnInit,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Subject } from 'rxjs';
import allPigeonsClassified from '../../services/classified';

// Types based on the classified data structure
interface PigeonTraits {
  base_color: string;
  main_pattern: string;
  is_spread: boolean;
  spread_level: string;
  is_piebald: boolean;
  piebald_level: string;
  piebald_pattern: string;
  piebald_intensity: {
    head: string;
    neck: string;
    body: string;
    tail: string;
  } | null;
  head_pattern: string;
  iridescence_level: string;
  wing_tip_color: string;
  tail_color: string;
  face_pattern: string;
}

interface ClassifiedPigeon {
  filename: string;
  looks_like_a_screenshot: number;
  is_bird: boolean;
  species: string;
  is_baby_pigeon: boolean;
  is_dead: boolean;
  distinctiveness: string;
  description: string;
  pigeon_traits: PigeonTraits | null;
  character: string;
  imageUrl?: string; // We'll add this for the image path
}

interface CharacterDistribution {
  [character: string]: number;
}

@Component({
  selector: 'app-pigeon-classifier-visualization',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatChipsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatBadgeModule,
    MatTooltipModule,
  ],
  templateUrl: './pigeon-classifier-visualization.component.html',
  styleUrls: ['./pigeon-classifier-visualization.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PigeonClassifierVisualizationComponent
  implements OnInit, OnDestroy
{
  private destroy$ = new Subject<void>();

  // Signals for reactive state management
  loading = signal<boolean>(false);
  allPigeons = signal<ClassifiedPigeon[]>([]);
  filteredPigeons = signal<ClassifiedPigeon[]>([]);
  characterDistribution = signal<CharacterDistribution>({});
  uniqueCharacters = signal<string[]>([]);
  uniqueDistinctiveness = signal<string[]>([]);

  // Filter signals
  selectedCharacter = signal<string>('');
  selectedDistinctiveness = signal<string>('');
  searchQuery = signal<string>('');
  sortBy = signal<'character' | 'distinctiveness' | 'filename'>('character');

  // Hover state
  hoveredPigeon = signal<ClassifiedPigeon | null>(null);
  showHoverPanel = signal<boolean>(false);
  hoverPanelPosition = signal<{ x: number; y: number }>({ x: 0, y: 0 });

  // Computed properties
  characterGroups = computed(() => {
    const pigeons = this.filteredPigeons();
    const groups = new Map<string, ClassifiedPigeon[]>();

    pigeons.forEach((pigeon) => {
      if (!groups.has(pigeon.character)) {
        groups.set(pigeon.character, []);
      }
      groups.get(pigeon.character)!.push(pigeon);
    });

    // Convert to array and sort by character name
    return Array.from(groups.entries())
      .map(([character, pigeons]) => ({ character, pigeons }))
      .sort((a, b) => a.character.localeCompare(b.character));
  });

  totalPigeons = computed(() => this.filteredPigeons().length);
  totalCharacters = computed(() => this.characterGroups().length);

  constructor() {}

  ngOnInit(): void {
    this.loading.set(true);
    this.loadPigeonData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadPigeonData(): void {
    // Transform the static data to include image URLs
    const pigeonsWithImages: ClassifiedPigeon[] = allPigeonsClassified.map(pigeon => ({
      ...pigeon,
      imageUrl: `assets/pigeon-images2/${pigeon.filename}`
    }));

    this.allPigeons.set(pigeonsWithImages);
    this.filteredPigeons.set(pigeonsWithImages);

    // Extract unique characters and distinctiveness values
    const characters = [...new Set(pigeonsWithImages.map(p => p.character))];
    const distinctiveness = [...new Set(pigeonsWithImages.map(p => p.distinctiveness))];

    this.uniqueCharacters.set(characters);
    this.uniqueDistinctiveness.set(distinctiveness);

    this.loading.set(false);
  }

  private applyFilters(): void {
    let filtered = [...this.allPigeons()];

    // Apply character filter
    if (this.selectedCharacter()) {
      filtered = filtered.filter(
        (p) => p.character === this.selectedCharacter()
      );
    }

    // Apply distinctiveness filter
    if (this.selectedDistinctiveness()) {
      filtered = filtered.filter(
        (p) => p.distinctiveness === this.selectedDistinctiveness()
      );
    }

    // Apply search filter
    if (this.searchQuery()) {
      const query = this.searchQuery().toLowerCase();
      filtered = filtered.filter(
        (p) =>
          p.filename.toLowerCase().includes(query) ||
          p.description.toLowerCase().includes(query) ||
          p.character.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    const sortBy = this.sortBy();
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'character':
          return a.character.localeCompare(b.character);
        case 'distinctiveness':
          return a.distinctiveness.localeCompare(b.distinctiveness);
        case 'filename':
          return a.filename.localeCompare(b.filename);
        default:
          return 0;
      }
    });

    this.filteredPigeons.set(filtered);
  }

  onCharacterFilterChange(character: string): void {
    this.selectedCharacter.set(character);
    this.applyFilters();
  }

  onDistinctivenessFilterChange(distinctiveness: string): void {
    this.selectedDistinctiveness.set(distinctiveness);
    this.applyFilters();
  }

  onSearchQueryChange(query: string): void {
    this.searchQuery.set(query);
    this.applyFilters();
  }

  onSortChange(sortBy: 'character' | 'distinctiveness' | 'filename'): void {
    this.sortBy.set(sortBy);
    this.applyFilters();
  }

  clearFilters(): void {
    this.selectedCharacter.set('');
    this.selectedDistinctiveness.set('');
    this.searchQuery.set('');
    this.applyFilters();
  }

  getCharacterDisplayName(character: string): string {
    // Convert character names to display names
    return character
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  getCharacterColor(character: string): string {
    // Simple color mapping based on character type
    const colorMap: { [key: string]: string } = {
      'BLUE_BAR': 'blue',
      'BLUE_CHECKER': 'blue',
      'BLUE_SPREAD_T_CHECK': 'blue',
      'BLUE_SPREAD_OTHER': 'blue',
      'BLUE_SPREAD_LIGHT_PIEBALD': 'blue',
      'MOTTLED_PIEBALD': 'purple',
      'MOTTLED_FACE': 'purple',
      'WHITE_PATCHES_PIEBALD': 'green',
      'WHITE_WING_TIPS': 'green',
      'WHITE_TAIL': 'green',
      'SMALL_WHITE_SPOTS_PIEBALD': 'green',
      'WEIRD_FACE_SPOTS': 'orange',
      'BALDHEAD_PIGEON': 'orange',
      'HELMET_PIGEON': 'orange',
      'NO_IRIDESCENCE': 'gray',
      'HIGH_IRIDESCENCE': 'indigo',
      'ASH_RED_BAR': 'red',
      'BABY_PIGEON': 'brown',
      'DEAD_PIGEON': 'gray',
      'RARE_DISTINCTIVENESS': 'red',
    };

    return colorMap[character] || 'indigo';
  }

  getCharacterColorHex(character: string): string {
    const colorMap: { [key: string]: string } = {
      blue: '#3B82F6',
      brown: '#A16207',
      red: '#DC2626',
      gray: '#6B7280',
      purple: '#9333EA',
      green: '#059669',
      orange: '#EA580C',
      indigo: '#4F46E5',
    };

    const color = this.getCharacterColor(character);
    return colorMap[color] || colorMap['indigo'];
  }

  onPigeonHover(pigeon: ClassifiedPigeon, event: MouseEvent): void {
    this.hoveredPigeon.set(pigeon);
    this.showHoverPanel.set(true);

    // Position the hover panel
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    const x = rect.right + 10;
    const y = rect.top;

    this.hoverPanelPosition.set({ x, y });
  }

  onPigeonLeave(): void {
    this.showHoverPanel.set(false);
    setTimeout(() => {
      if (!this.showHoverPanel()) {
        this.hoveredPigeon.set(null);
      }
    }, 100);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    img.src = 'assets/placeholder-pigeon.png';
  }
}
