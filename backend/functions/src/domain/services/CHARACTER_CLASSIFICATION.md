# Pigeon Character Classification System

## Overview

The pigeon character classification system categorizes pigeons into **30 distinct game characters** based on their phenotype analysis. The classification is hierarchical and does NOT use the `distinctiveness` field.

## Classification Hierarchy

### 1. UNCLASSIFIED (1 character)
- **UNCLASSIFIED**: Non-birds, dead non-pigeons, or unknown species

### 2. Non-Pigeon Species (9 characters)
- **MAGPIE**: Magpies
- **WOOD_PIGEON**: Wood pigeons
- **TURTLEDOVE**: Turtle doves
- **DUCK**: Ducks
- **CROW**: Crows
- **RAVEN**: Ravens
- **SEAGULL**: Seagulls
- **ROOSTER**: Roosters and hens
- **OTHER_BIRD**: Other bird species

### 3. Special Cases (2 characters)
- **BABY_PIGEON**: Baby pigeons (squabs)
- **DEAD_PIGEON**: Dead columba_livia pigeons

### 4. Columba Livia - Other Colors (1 character)
- **OTHER_COLOR**: Pigeons with base_color = "other"

### 5. Columba Livia - Ash-Red (1 character)
- **ASH_RED**: All ash-red pigeons (single character for now)

### 6. Columba Livia - Brown (1 character)
- **BROWN**: All brown pigeons (single character for now)

### 7. Columba Livia - Blue Classic (7 characters)
Blue pigeons WITHOUT strong piebald characteristics:

#### Head/Face Pattern Characters:
- **BLUE_HELMET**: Pigeons with helmet or baldhead head_pattern
- **BLUE_FACE_MOTTLED**: Pigeons with mottled face_pattern
- **BLUE_FACE_WEIRD_SPOTS**: Pigeons with weird_spots face_pattern

#### Body Pattern Characters:
- **BLUE_BAR**: Blue pigeons with bar pattern
- **BLUE_CHECKER**: Blue pigeons with checker pattern
- **BLUE_T_CHECK**: Blue pigeons with t-check pattern
- **BLUE_OTHER**: Blue pigeons with other/barless patterns

### 8. Columba Livia - Blue Original (8 characters)
Blue pigeons WITH strong piebald characteristics.

**Strong Piebald Definition**: 
- Intermediate+ piebald on body AND
- Intermediate+ piebald on at least one other spot (head, neck, or tail)

#### Bar Pattern with Strong Piebald (2 characters):
- **BLUE_BAR_PIEBALD_BODY**: Piebald mainly on body/tail
- **BLUE_BAR_PIEBALD_HEAD**: Piebald includes head

#### Checker Pattern with Strong Piebald (3 characters):
- **BLUE_CHECKER_PIEBALD_BODY**: Piebald mainly on body/tail/neck
- **BLUE_CHECKER_PIEBALD_HEAD**: Piebald includes head (but not neck)
- **BLUE_CHECKER_PIEBALD_FULL**: Piebald includes head AND neck

#### Other Pattern with Strong Piebald (3 characters):
- **BLUE_OTHER_PIEBALD_BODY**: Piebald mainly on body/tail/neck
- **BLUE_OTHER_PIEBALD_HEAD**: Piebald includes head (but not neck)
- **BLUE_OTHER_PIEBALD_FULL**: Piebald includes head AND neck

## Classification Algorithm

```
1. Check if not a bird → UNCLASSIFIED
2. Check if dead and not columba_livia → UNCLASSIFIED
3. Check for non-pigeon species → specific species character
4. Check if baby pigeon → BABY_PIGEON
5. Check if dead pigeon → DEAD_PIGEON
6. Check base_color:
   - other → OTHER_COLOR
   - ash-red → ASH_RED
   - brown → BROWN
   - blue → continue to blue classification
7. For blue pigeons:
   a. Check if has strong piebald
   b. If strong piebald → Original path
   c. If not strong piebald → Classic path
8. Classic path:
   - Check head_pattern (helmet, baldhead) → BLUE_HELMET
   - Check face_pattern (mottled, weird_spots) → BLUE_FACE_MOTTLED, BLUE_FACE_WEIRD_SPOTS
   - Otherwise check main_pattern → BLUE_BAR, BLUE_CHECKER, BLUE_T_CHECK, BLUE_OTHER
9. Original path (strong piebald):
   - First separate by main_pattern (bar, checker, other)
   - Then by piebald intensity location:
     * If head AND neck have intermediate+ → FULL variant
     * Else if head has intermediate+ → HEAD variant
     * Else → BODY variant
```

## Character Distribution (from analysis_results.json)

Based on 166 analyzed images:

| Character | Count | Category |
|-----------|-------|----------|
| BLUE_BAR | 42 | Blue classic |
| BLUE_CHECKER | 31 | Blue classic |
| BROWN | 13 | Brown |
| BLUE_OTHER_PIEBALD_FULL | 11 | Blue original |
| BLUE_T_CHECK | 8 | Blue classic |
| BLUE_CHECKER_PIEBALD_FULL | 6 | Blue original |
| BLUE_OTHER | 6 | Blue classic |
| ASH_RED | 5 | Ash-red |
| BLUE_FACE_MOTTLED | 5 | Blue classic |
| BLUE_OTHER_PIEBALD_BODY | 4 | Blue original |
| BLUE_FACE_WEIRD_SPOTS | 3 | Blue classic |
| BLUE_HELMET | 3 | Blue classic |
| BLUE_CHECKER_PIEBALD_BODY | 3 | Blue original |
| BLUE_OTHER_PIEBALD_HEAD | 3 | Blue original |
| WOOD_PIGEON | 3 | Non-pigeon |
| BLUE_BAR_PIEBALD_BODY | 2 | Blue original |
| BLUE_CHECKER_PIEBALD_HEAD | 2 | Blue original |
| BABY_PIGEON | 2 | Special |
| DEAD_PIGEON | 2 | Special |
| SEAGULL | 2 | Non-pigeon |
| BLUE_BAR_PIEBALD_HEAD | 1 | Blue original |
| OTHER_COLOR | 1 | Other |
| UNCLASSIFIED | 1 | Unclassified |
| MAGPIE | 1 | Non-pigeon |
| TURTLEDOVE | 1 | Non-pigeon |
| DUCK | 1 | Non-pigeon |
| CROW | 1 | Non-pigeon |
| RAVEN | 1 | Non-pigeon |
| ROOSTER | 1 | Non-pigeon |
| OTHER_BIRD | 1 | Non-pigeon |

**Total: 30 unique characters**

## Design Rationale

1. **Simplified Color Categories**: Ash-red and brown pigeons are grouped into single characters for now, as they are less common in the dataset (5 and 13 instances respectively).

2. **Strong Piebald Threshold**: The "strong piebald" definition ensures that only pigeons with significant white markings across multiple body parts are classified in the "original" path, creating more distinctive and interesting characters.

3. **Head/Face Priority**: In the classic path, head and face patterns are checked before body patterns, as they are more visually distinctive features.

4. **Pattern-First Approach**: In the original path, pigeons are first separated by their base pattern (bar, checker, other), then by where the piebald markings are most intense, creating logical subcategories.

5. **No Distinctiveness**: The classification does NOT use the `distinctiveness` field, focusing purely on observable physical traits.

## Future Expansion

If more granularity is needed in the future:
- Brown pigeons could be split by pattern (bar, checker, other)
- Ash-red pigeons could be split by pattern
- Blue classic pigeons could be further subdivided based on spread mutations
- Additional piebald intensity levels could be added

