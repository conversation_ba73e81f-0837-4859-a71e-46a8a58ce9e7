#!/usr/bin/env ts-node

import * as fs from "fs";
import * as path from "path";
import OpenAI from "openai";
import { PigeonDistinctiveness } from "../domain/enums/PigeonDistinctiveness";
import { BirdType } from "../domain/enums/BirdType";

// Load environment variables
import "../env";

// Type for the OpenAI analysis response
interface PigeonAnalysisResult {
    filename: string;
    looks_like_a_screenshot: number; // 1 to 10
    is_bird: boolean;
    bird_type: string; // "pigeon" | "crow" | "seagull" | "sparrow" | "magpie"
    is_baby_pigeon: boolean;
    is_dead: boolean;
    // distinctiveness: string; // "COMMON", "UNUSUAL", "RARE"
    // description: string; // Short description (max 80 words) of the pigeon's plumage and patterns
    error?: string; // In case of processing error
}

class ImageAnalyzer {
    private openAIClient: OpenAI;
    private tmpDir: string;
    private resultsDir: string;
    private results: PigeonAnalysisResult[] = [];

    constructor() {
        this.openAIClient = new OpenAI();
        this.tmpDir = path.join(__dirname, "../tmp");
        this.resultsDir = path.join(__dirname, "../tmp/results");
    }

    /**
     * Get all image files from the tmp directory
     */
    private getImageFiles(): string[] {
        const files = fs.readdirSync(this.tmpDir);
        return files.filter((file) => {
            const ext = path.extname(file).toLowerCase();
            return [".jpg", ".jpeg", ".png", ".gif", ".webp"].includes(ext);
        });
    }

    /**
     * Convert local image file to base64 data URL for OpenAI
     */
    private imageToDataUrl(filePath: string): string {
        const imageBuffer = fs.readFileSync(filePath);
        const ext = path.extname(filePath).toLowerCase();
        let mimeType = "image/jpeg";

        switch (ext) {
            case ".png":
                mimeType = "image/png";
                break;
            case ".gif":
                mimeType = "image/gif";
                break;
            case ".webp":
                mimeType = "image/webp";
                break;
        }

        return `data:${mimeType};base64,${imageBuffer.toString("base64")}`;
    }

    /**
     * Analyze a single image using OpenAI
     */
    private async analyzeImage(filename: string): Promise<PigeonAnalysisResult> {
        console.log(`Analyzing ${filename}...`);

        try {
            const filePath = path.join(this.tmpDir, filename);
            const imageDataUrl = this.imageToDataUrl(filePath);

            //
            // 1) Structured Outputs JSON Schema (as expected by OpenAI)
            //    Docs: https://platform.openai.com/docs/guides/structured-outputs
            //
            const BirdPhenotypeSchema = {
                name: "BirdPhenotype",
                strict: true,
                schema: {
                    type: "object",
                    additionalProperties: false,
                    properties: {
                        looks_like_a_screenshot: { type: "integer", minimum: 1, maximum: 10 },
                        // image_quality: { type: "integer", minimum: 1, maximum: 10 },
                        is_bird: { type: "boolean" },

                        species: {
                            type: "string",
                            enum: [
                                "columba_livia",
                                "turtledove",
                                "wood_pigeon",
                                "dove",
                                "sparrow",
                                "seagull",
                                "gull",
                                "crow",
                                "raven",
                                "starling",
                                "hen",
                                "rooster",
                                "duck",
                                "magpie",
                                "other",
                            ],
                        },

                        is_baby_pigeon: { type: "boolean" },
                        is_dead: { type: "boolean" },
                        distinctiveness: { type: "string", enum: ["common", "unusual", "rare"] },

                        description: {
                            type: "string",
                            description: "Short description (max 80 words) of the bird's plumage and patterns. State clearly if you find some originality on the bird.",
                            maxLength: 350, // ≤50 words; character cap is safer for JSON Schema
                        },

                        // confidence: { type: "integer", minimum: 1, maximum: 10 },

                        // behavior: { type: ["string", "null"] },
                        // context: { type: ["string", "null"] },

                        // Pigeon-specific traits: object or null (null when species != "columba_livia")
                        pigeon_traits: {
                            type: ["object", "null"],
                            additionalProperties: false,
                            properties: {
                                base_color: {
                                    type: "string",
                                    enum: ["blue", "ash-red", "brown", "other"],
                                },
                                main_pattern: {
                                    type: "string",
                                    description: "Bar: two black bars. Checker: spotted pattern, darker. T-check: dense checkering, nearly solid. Barless: no visible bars. Other: none of the above.",
                                    enum: ["bar", "checker", "t-check", "barless", "other"],
                                },
                                is_spread: { type: "boolean", description: "True if the pigeon has the spread mutation, meaning that the main color covers the entire body." },
                                spread_level: {
                                    type: "string",
                                    enum: ["none", "partial", "full"],
                                },
                                // is_dilute: { type: "boolean" },
                                // dilute_level: {
                                //     type: "string",
                                //     enum: ["none", "light", "full"],
                                // },

                                is_piebald: { type: "boolean", description: "True if the pigeon has the piebald mutation, meaning that it has white patches. Minor white feathers do not count." },
                                piebald_level: {
                                    type: "string",
                                    enum: ["none", "intermediate", "heavy", "full_white"],
                                },
                                piebald_pattern: {
                                    type: "string",
                                    description: "Main pattern of piebald manifestation on the pigeon. Only relevant if is_piebald is true. White patches are clearly delimited white zones. Small white spots are small white spots :). Mottled is white feathers mixed with other colors.",
                                    enum: ["none", "white_patches", "small_white_spots", "mottled"],
                                },
                                piebald_intensity: {
                                    type: ["object", "null"],
                                    // description: "Intensity of piebald on the pigeon. Only relevant if is_piebald is true. Minor white feathers do not count !",
                                    description: "Percentage of the area covered with white.",
                                    additionalProperties: false,
                                    properties: {
                                        head: {
                                            type: "string",
                                            description: "Head goes from beak to shoulders",
                                            enum: ["O%", "0%-10%", "10%-40%", "40%-80%", ">80%"],
                                        },
                                        // neck: {
                                        //     type: "string",
                                        //     description: "Neck is between head and body. Can include the upper back of the pigeon",
                                        //     enum: ["0%-10%", "10%-40%", "40%-80%", ">80%"],
                                        // },
                                        body: {
                                            type: "string",
                                            description: "Body includes back, wings, and belly",
                                            enum: ["0%-10%", "10%-40%", "40%-80%", ">80%"],
                                        },
                                        // wings: {
                                        //     type: "string",
                                        //     enum: ["none", "light", "intermediate", "heavy", "fully_white"],
                                        // },
                                        tail: {
                                            type: "string",
                                            description: "Tail is the feathers at the end of the body",
                                            enum: ["0%-10%", "10%-40%", "40%-80%", ">80%"],
                                        },
                                        // belly: {
                                        //     type: "string",
                                        //     enum: ["none", "light", "intermediate", "heavy", "full"],
                                        // },
                                    },
                                    required: ["head", "body", "tail"],
                                    // required: ["head", "neck", "body", "tail"],
                                },

                                head_pattern: {
                                    type: "string",
                                    description: "Helmet if the pigeon has a white cap. Baldhead if the entire head is white. None otherwise.",
                                    enum: ["helmet", "baldhead", "none"],
                                },
                                // neck_pattern: {
                                //     type: "string",
                                //     enum: ["white_collar", "none"],
                                // },
                                // body_white_pattern: {
                                //     type: "string",
                                //     enum: ["saddle", "shield", "rump", "vent", "mottled", "none", "other"],
                                // },
                                // has_grizzle: { type: "boolean" },
                                // has_recessive_red: { type: "boolean" },

                                // visible_color: {
                                //     type: "string",
                                //     enum: ["blue", "black", "brown", "ash-red", "silver", "white", "mixed", "other"],
                                // },
                                iridescence_level: {
                                    type: "string",
                                    description: "Level of green / purple iridescence on the neck",
                                    enum: ["none", "low", "medium", "high"],
                                },
                                wing_tip_color: {
                                    type: "string",
                                    description: "Color of the wing tips. Carefully spot the wing tip as pigeons are mainly shot when they are walking so do not confuse with the tail.",
                                    enum: ["black", "dark", "grey", "light", "white"],
                                },
                                tail_color: {
                                    type: "string",
                                    description: "Color of the tail feathers. Often located between the wing tips, do not confuse with the wing tips.",
                                    enum: ["black", "dark", "light", "white", "mixed"],
                                },
                                // special_face_mark: { type: ["string", "null"] },
                                face_pattern: {
                                    type: "string",
                                    description: "Pattern of the face. Uniform if the face is a single color. Mottled if there are multiple colors. Weird spots if there are spots or patches.",
                                    enum: ["uniform_standard", "mottled", "weird_spots"],
                                },
                                // dirtiness_level: {
                                //     type: "string",
                                //     enum: ["clean", "average", "dirty"],
                                // },
                                // body_size: {
                                //     type: "string",
                                //     enum: ["slim", "average", "plump", "very_plump"],
                                // },
                                // has_broken_leg: { type: "boolean" },
                                // has_banded_leg: { type: "boolean" },
                                // other_notable_trait: { type: ["string", "null"] },
                            },
                            required: [
                                "base_color",
                                "main_pattern",
                                "is_spread",
                                "spread_level",
                                // "is_dilute",
                                // "dilute_level",
                                "is_piebald",
                                "piebald_level",
                                "piebald_pattern",
                                "piebald_intensity",
                                "head_pattern",
                                // "neck_pattern",
                                // "body_white_pattern",
                                // "has_grizzle",
                                // "has_recessive_red",
                                // "visible_color",
                                "iridescence_level",
                                "tail_color",
                                // "special_face_mark",
                                "face_pattern",
                                "wing_tip_color",
                                // "dirtiness_level",
                                // "body_size",
                                // "has_broken_leg",
                                // "has_banded_leg",
                                // "other_notable_trait",
                            ],
                        },
                    },
                    required: [
                        "looks_like_a_screenshot",
                        // "image_quality",
                        "is_bird",
                        "species",
                        "is_baby_pigeon",
                        "is_dead",
                        "distinctiveness",
                        "description",
                        // "confidence",
                        // "behavior",
                        // "context",
                        "pigeon_traits",
                    ],
                },
            } as const;

            //
            // 2) System prompt crafted from your “Persona” + “Task”
            //
            const SYSTEM_PROMPT = `
You are a bird phenotype analysis expert, trained to identify and classify birds from photos.
You specialize in urban birds, especially feral pigeons (Columba livia / pigeon biset), and can detect fine-grained visual traits related to genetics, plumage, and morphology.

Task:
- Analyze the image and return a valid JSON object describing the central bird.
- If multiple birds: analyze only the one closest to the center.
- If no bird is visible: return "is_bird": false.
- Use null for any uncertain value.
- Do not describe: posture, attitude, orientation, lighting, or camera angle.
- Follow the field definitions strictly.

Important:
- If species != "columba_livia", set "pigeon_traits": null.
- If species == "columba_livia", fill "pigeon_traits" according to the schema and return null for unknown subfields.
`.trim();

            const resp = await this.openAIClient.responses.create({
                model: "gpt-5-mini", // any vision-capable model that supports structured outputs
                // temperature: 0,
                reasoning: { effort: "low" },
                input: [
                    {
                        role: "system",
                        content: SYSTEM_PROMPT,
                    },
                    {
                        role: "user",
                        content: [
                            {
                                type: "input_text",
                                text: "Analyze this image and return the BirdPhenotype JSON.",
                            },
                            {
                                type: "input_image",
                                detail: "high",
                                image_url: imageDataUrl,
                            },
                        ],
                    },
                ],
                text: {
                    format: {
                        type: "json_schema",
                        name: BirdPhenotypeSchema.name,
                        schema: BirdPhenotypeSchema.schema,
                        strict: BirdPhenotypeSchema.strict,
                    },
                },
            });
            if (resp.error) {
                throw new Error(`OpenAI error: ${resp.error.message}`);
            }
            if (!resp.output_text) {
                throw new Error("No response from OpenAI for pigeon analysis");
            }

            // The SDK returns JSON already parsed when using structured outputs
            const parsedResult = JSON.parse(resp.output_text);
            return {
                filename,
                ...parsedResult,
            };
        } catch (error) {
            console.error(`Error analyzing ${filename}:`, error);
            return {
                filename,
                looks_like_a_screenshot: 0,
                is_bird: false,
                bird_type: BirdType.PIGEON,
                is_baby_pigeon: false,
                is_dead: false,
                // distinctiveness: PigeonDistinctiveness.COMMON,
                // description: "",
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }

    /**
     * Process all images in the tmp directory
     */
    async processAllImages(): Promise<void> {
        const imageFiles = this.getImageFiles();
        console.log(`Found ${imageFiles.length} image files to process`);

        // Process images with a delay to avoid rate limiting
        for (let i = 0; i < imageFiles.length; i++) {
            const filename = imageFiles[i];
            console.log(`Processing ${i + 1}/${imageFiles.length}: ${filename}`);

            const result = await this.analyzeImage(filename);
            this.results.push(result);

            // Add a small delay between requests to avoid rate limiting
            if (i < imageFiles.length - 1) {
                await new Promise((resolve) => setTimeout(resolve, 1000));
            }
        }
    }


    async processOneSpecificImage(): Promise<void> {
        const filename = "chair.jpg";
        const result = await this.analyzeImage(filename);
        this.results.push(result);
    }

    /**
     * Save results to JSON file
     */
    saveResults(): void {
        const outputPath = path.join(__dirname, "../tmp/analysis_results.json");
        fs.writeFileSync(outputPath, JSON.stringify(this.results, null, 2));
        console.log(`Results saved to ${outputPath}`);

        // Also save a summary
        const summary = {
            total_images: this.results.length,
            successful_analyses: this.results.filter((r) => !r.error).length,
            failed_analyses: this.results.filter((r) => r.error).length,
            birds_detected: this.results.filter((r) => r.is_bird).length,
            pigeons: this.results.filter((r) => r.bird_type === BirdType.PIGEON).length,
        };

        const summaryPath = path.join(__dirname, "../tmp/analysis_summary.json");
        fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
        console.log(`Summary saved to ${summaryPath}`);
    }
}

/**
 * Compare analysis results with evaluation set
 */
async function compareAnalysisWithEvaluation(): Promise<void> {
    console.log("Starting comparison between analysis results and evaluation set...");

    try {
        // Load both JSON files
        const analysisResultsPath = path.join(__dirname, "../tmp/analysis_results.json");
        const evaluationSetPath = path.join(__dirname, "evaluation_set.json");

        if (!fs.existsSync(analysisResultsPath)) {
            throw new Error(`Analysis results file not found: ${analysisResultsPath}`);
        }
        if (!fs.existsSync(evaluationSetPath)) {
            throw new Error(`Evaluation set file not found: ${evaluationSetPath}`);
        }

        const analysisResults = JSON.parse(fs.readFileSync(analysisResultsPath, "utf8"));
        const evaluationSet = JSON.parse(fs.readFileSync(evaluationSetPath, "utf8"));

        console.log(`Loaded ${analysisResults.length} analysis results`);
        console.log(`Loaded ${evaluationSet.length} evaluation entries`);

        // Create a map of evaluation data by filename for quick lookup
        const evaluationMap = new Map();
        evaluationSet.forEach((entry: any) => {
            evaluationMap.set(entry.filename, entry);
        });

        let totalComparisons = 0;
        let exactMatches = 0;

        // Compare each analysis result with corresponding evaluation entry
        for (const analysisEntry of analysisResults) {
            const filename = analysisEntry.filename;
            const evaluationEntry = evaluationMap.get(filename);

            if (!evaluationEntry) {
                console.log(`⚠️  No evaluation data found for: ${filename}`);
                continue;
            }

            totalComparisons++;
            console.log(`\n🔍 Comparing: ${filename}`);

            // Compare the entries (excluding description field)
            const differences = compareEntries(analysisEntry, evaluationEntry);

            if (differences.length === 0) {
                console.log(`✅ EXACT MATCH: ${filename}`);
                exactMatches++;
            } else {
                console.log(`❌ DIFFERENCES FOUND: ${filename}`);
                differences.forEach(diff => console.log(`   ${diff}`));
            }
        }

        // Summary
        console.log(`\n📊 COMPARISON SUMMARY:`);
        console.log(`Total comparisons: ${totalComparisons}`);
        console.log(`Exact matches: ${exactMatches}`);
        console.log(`Entries with differences: ${totalComparisons - exactMatches}`);
        console.log(`Match rate: ${((exactMatches / totalComparisons) * 100).toFixed(2)}%`);

    } catch (error) {
        console.error("Comparison failed:", error);
        throw error;
    }
}

/**
 * Compare two entries and return human-readable differences
 */
function compareEntries(analysis: any, evaluation: any): string[] {
    const differences: string[] = [];

    // Compare top-level fields (excluding description)
    const topLevelFields = [
        'looks_like_a_screenshot',
        'is_bird',
        'species',
        'is_baby_pigeon',
        'is_dead',
        'distinctiveness'
    ];

    for (const field of topLevelFields) {
        if (analysis[field] !== evaluation[field]) {
            differences.push(`${field}: analysis="${analysis[field]}" vs evaluation="${evaluation[field]}"`);
        }
    }

    // Compare pigeon_traits if both exist
    if (analysis.pigeon_traits && evaluation.pigeon_traits) {
        const traitDifferences = comparePigeonTraits(analysis.pigeon_traits, evaluation.pigeon_traits);
        differences.push(...traitDifferences);
    } else if (analysis.pigeon_traits !== evaluation.pigeon_traits) {
        differences.push(`pigeon_traits: analysis="${analysis.pigeon_traits ? 'present' : 'null'}" vs evaluation="${evaluation.pigeon_traits ? 'present' : 'null'}"`);
    }

    return differences;
}

/**
 * Compare pigeon traits and return human-readable differences
 */
function comparePigeonTraits(analysisTraits: any, evaluationTraits: any): string[] {
    const differences: string[] = [];

    // Define all possible trait fields
    const traitFields = [
        'base_color',
        'white_flights',
        'main_pattern',
        'is_spread',
        'spread_level',
        'is_piebald',
        'piebald_level',
        'piebald_pattern',
        'head_pattern',
        'iridescence_level',
        'wing_tip_color',
        'tail_color',
        'face_pattern'
    ];

    for (const field of traitFields) {
        if (analysisTraits[field] !== evaluationTraits[field]) {
            differences.push(`pigeon_traits.${field}: analysis="${analysisTraits[field]}" vs evaluation="${evaluationTraits[field]}"`);
        }
    }

    // Special handling for piebald_intensity (nested object)
    if (analysisTraits.piebald_intensity || evaluationTraits.piebald_intensity) {
        const intensityDifferences = comparePiebaldIntensity(
            analysisTraits.piebald_intensity,
            evaluationTraits.piebald_intensity
        );
        differences.push(...intensityDifferences);
    }

    return differences;
}

/**
 * Compare piebald intensity objects
 */
function comparePiebaldIntensity(analysisIntensity: any, evaluationIntensity: any): string[] {
    const differences: string[] = [];

    // If one is null and the other isn't
    if (!analysisIntensity && evaluationIntensity) {
        differences.push(`pigeon_traits.piebald_intensity: analysis="null" vs evaluation="present"`);
        return differences;
    }
    if (analysisIntensity && !evaluationIntensity) {
        differences.push(`pigeon_traits.piebald_intensity: analysis="present" vs evaluation="null"`);
        return differences;
    }
    if (!analysisIntensity && !evaluationIntensity) {
        return differences; // Both null, no difference
    }

    // Compare intensity fields
    const intensityFields = ['head', 'body', 'tail'];
    for (const field of intensityFields) {
        if (analysisIntensity[field] !== evaluationIntensity[field]) {
            differences.push(`pigeon_traits.piebald_intensity.${field}: analysis="${analysisIntensity[field]}" vs evaluation="${evaluationIntensity[field]}"`);
        }
    }

    return differences;
}

// Main execution
async function main() {
    console.log("Starting image analysis script...");

    try {
        // Uncomment the task you want to run:

        // For image analysis:
        // const analyzer = new ImageAnalyzer();
        // await analyzer.processAllImages();
        // await analyzer.processOneSpecificImage();
        // analyzer.saveResults();

        // For comparison task:
        await compareAnalysisWithEvaluation();

        console.log("Task completed successfully!");
    } catch (error) {
        console.error("Script failed:", error);
        process.exit(1);
    }
}

// Run the script if called directly
if (require.main === module) {
    main();
}
